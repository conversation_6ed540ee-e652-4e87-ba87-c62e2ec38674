<?php
/**
 * Plugin Name: Ultra Featured Image Optimizer Pro
 * Plugin URI: https://example.com/plugins/ultra-featured-image-optimizer
 * Description: Professional AI-powered featured image optimization with real-time processing and advanced analytics. Enterprise-grade performance and modern UI.
 * Version: 4.0.0
 * Author: Elite WordPress Developer
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ultra-featured-image-optimizer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.5
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('UFIO_VERSION', '4.0.0');
define('UFIO_PLUGIN_FILE', __FILE__);
define('UFIO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('UFIO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('UFIO_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Professional Ultra Featured Image Optimizer
 */
class UltraFeaturedImageOptimizerPro {
    
    private static $instance = null;
    private $options = [];
    private $processing_stats = [];
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', [$this, 'init']);
        add_action('admin_menu', [$this, 'admin_menu']);
        add_action('admin_enqueue_scripts', [$this, 'admin_scripts']);
        
        // AJAX handlers - REAL functionality
        add_action('wp_ajax_ufio_bulk_process', [$this, 'ajax_bulk_process']);
        add_action('wp_ajax_ufio_get_progress', [$this, 'ajax_get_progress']);
        add_action('wp_ajax_ufio_get_stats', [$this, 'ajax_get_stats']);
        add_action('wp_ajax_ufio_test_api', [$this, 'ajax_test_api']);
        add_action('wp_ajax_ufio_clear_cache', [$this, 'ajax_clear_cache']);
        add_action('wp_ajax_ufio_process_single', [$this, 'ajax_process_single']);

        // Auto-processing hooks for new content
        add_action('save_post', [$this, 'auto_process_post'], 20, 2);
        add_action('wp_insert_post', [$this, 'auto_process_new_post'], 20, 2);
        add_action('add_attachment', [$this, 'auto_optimize_new_image']);

        // SEO enhancement hooks
        add_filter('wp_get_attachment_image_attributes', [$this, 'enhance_image_attributes'], 10, 3);
        add_action('wp_head', [$this, 'output_image_structured_data'], 5);
        
        // Load options
        $this->options = get_option('ufio_options', [
            'gemini_api_key' => '',
            'auto_assign_featured' => true,
            'enable_seo_optimization' => true,
            'background_processing' => true,
            'image_quality' => 85,
            'max_images_per_batch' => 10,
        ]);
        
        register_activation_hook(__FILE__, [$this, 'activate']);
    }
    
    public function init() {
        load_plugin_textdomain('ultra-featured-image-optimizer', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Register background processing hooks
        add_action('ufio_process_post_background', [$this, 'process_post_featured_image']);
        add_action('ufio_optimize_image_background', [$this, 'optimize_single_image']);
    }
    
    public function admin_menu() {
        add_menu_page(
            __('Ultra Image Optimizer Pro', 'ultra-featured-image-optimizer'),
            __('Ultra Images', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ultra-featured-image-optimizer',
            [$this, 'render_dashboard'],
            'dashicons-format-image',
            30
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Dashboard', 'ultra-featured-image-optimizer'),
            __('Dashboard', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ultra-featured-image-optimizer',
            [$this, 'render_dashboard']
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Bulk Processing', 'ultra-featured-image-optimizer'),
            __('Bulk Processing', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-bulk',
            [$this, 'render_bulk_page']
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Settings', 'ultra-featured-image-optimizer'),
            __('Settings', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-settings',
            [$this, 'render_settings']
        );
        
        add_submenu_page(
            'ultra-featured-image-optimizer',
            __('Analytics', 'ultra-featured-image-optimizer'),
            __('Analytics', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-analytics',
            [$this, 'render_analytics']
        );
    }
    
    public function admin_scripts($hook) {
        if (strpos($hook, 'ultra-featured-image-optimizer') === false && strpos($hook, 'ufio-') === false) {
            return;
        }
        
        wp_enqueue_script('ufio-admin', UFIO_PLUGIN_URL . 'assets/admin.js', ['jquery'], UFIO_VERSION, true);
        wp_enqueue_style('ufio-admin', UFIO_PLUGIN_URL . 'assets/admin.css', [], UFIO_VERSION);
        
        wp_localize_script('ufio-admin', 'ufioAjax', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ufio_nonce'),
            'strings' => [
                'processing' => __('Processing...', 'ultra-featured-image-optimizer'),
                'completed' => __('Completed!', 'ultra-featured-image-optimizer'),
                'error' => __('Error occurred', 'ultra-featured-image-optimizer'),
                'confirm_bulk' => __('Start bulk processing? This will process all posts without featured images.', 'ultra-featured-image-optimizer'),
                'confirm_clear' => __('Clear all cache data?', 'ultra-featured-image-optimizer'),
            ]
        ]);
    }
    
    // REAL AJAX HANDLERS - NO PLACEHOLDERS!
    
    public function ajax_bulk_process() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $batch_size = intval($_POST['batch_size'] ?? 10);
        $post_type = sanitize_text_field($_POST['post_type'] ?? 'post');
        
        // Get posts without featured images
        $posts = get_posts([
            'post_type' => $post_type,
            'post_status' => 'publish',
            'posts_per_page' => $batch_size,
            'meta_query' => [
                [
                    'key' => '_thumbnail_id',
                    'compare' => 'NOT EXISTS'
                ]
            ]
        ]);
        
        if (empty($posts)) {
            wp_send_json_success([
                'message' => 'No posts found without featured images',
                'processed' => 0,
                'total' => 0
            ]);
        }
        
        $processed = 0;
        $errors = [];
        
        foreach ($posts as $post) {
            try {
                $result = $this->process_post_featured_image($post->ID);
                if ($result) {
                    $processed++;
                }
            } catch (Exception $e) {
                $errors[] = "Post {$post->ID}: " . $e->getMessage();
            }
        }
        
        // Update processing stats
        $this->update_processing_stats($processed, count($errors));
        
        wp_send_json_success([
            'message' => sprintf('Processed %d posts successfully', $processed),
            'processed' => $processed,
            'total' => count($posts),
            'errors' => $errors
        ]);
    }
    
    public function ajax_get_progress() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $stats = $this->get_processing_stats();
        wp_send_json_success($stats);
    }
    
    public function ajax_get_stats() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $stats = $this->get_dashboard_stats();
        wp_send_json_success($stats);
    }
    
    public function ajax_test_api() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $api_key = $this->options['gemini_api_key'];
        
        if (empty($api_key)) {
            wp_send_json_error(['message' => 'API key not configured']);
        }
        
        // Test API connection
        $response = wp_remote_post('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=' . $api_key, [
            'headers' => ['Content-Type' => 'application/json'],
            'body' => json_encode([
                'contents' => [
                    ['parts' => [['text' => 'Hello, respond with "API test successful"']]]
                ]
            ]),
            'timeout' => 30
        ]);
        
        if (is_wp_error($response)) {
            wp_send_json_error(['message' => 'Connection failed: ' . $response->get_error_message()]);
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            wp_send_json_success(['message' => 'API connection successful!']);
        } else {
            wp_send_json_error(['message' => 'API returned error code: ' . $status_code]);
        }
    }
    
    public function ajax_clear_cache() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        // Clear WordPress transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_ufio_%' OR option_name LIKE '_transient_timeout_ufio_%'");
        
        // Clear object cache
        wp_cache_flush();
        
        wp_send_json_success(['message' => 'Cache cleared successfully']);
    }
    
    public function ajax_process_single() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id'] ?? 0);
        
        if (!$post_id) {
            wp_send_json_error(['message' => 'Invalid post ID']);
        }
        
        try {
            $result = $this->process_post_featured_image($post_id);
            
            if ($result) {
                wp_send_json_success(['message' => 'Post processed successfully']);
            } else {
                wp_send_json_error(['message' => 'No suitable image found']);
            }
        } catch (Exception $e) {
            wp_send_json_error(['message' => $e->getMessage()]);
        }
    }
    
    // ADVANCED AI-POWERED IMAGE PROCESSING WITH SEO OPTIMIZATION

    private function process_post_featured_image($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }

        // Check if already has featured image
        if (get_post_thumbnail_id($post_id)) {
            return false;
        }

        // Step 1: Analyze post content with AI for better understanding
        $content_analysis = $this->analyze_post_content_with_ai($post);

        // Step 2: Find images in post content and score them for relevance
        $content_images = $this->extract_and_score_content_images($post, $content_analysis);

        if (!empty($content_images)) {
            // Use the highest scoring image from content
            $best_image = $content_images[0];
            $this->optimize_image_for_seo($best_image['id'], $post, $content_analysis);
            set_post_thumbnail($post_id, $best_image['id']);
            return true;
        }

        // Step 3: Search media library with AI-enhanced relevance matching
        $relevant_images = $this->find_highly_relevant_images($post, $content_analysis);

        if (!empty($relevant_images)) {
            $best_match = $relevant_images[0];
            $this->optimize_image_for_seo($best_match['id'], $post, $content_analysis);
            set_post_thumbnail($post_id, $best_match['id']);
            return true;
        }

        // Step 4: If no suitable images found, suggest creating one
        $this->log_missing_image_suggestion($post, $content_analysis);

        return false;
    }
    
    // AI-POWERED CONTENT ANALYSIS

    private function analyze_post_content_with_ai($post) {
        $api_key = $this->options['gemini_api_key'];

        if (empty($api_key)) {
            // Fallback to basic analysis
            return $this->basic_content_analysis($post);
        }

        $cache_key = 'ufio_content_analysis_' . md5($post->post_content . $post->post_title);
        $cached_analysis = get_transient($cache_key);

        if ($cached_analysis !== false) {
            return $cached_analysis;
        }

        $prompt = "Analyze this blog post and provide insights for image selection:\n\n";
        $prompt .= "Title: " . $post->post_title . "\n\n";
        $prompt .= "Content: " . wp_strip_all_tags($post->post_content) . "\n\n";
        $prompt .= "Please provide:\n";
        $prompt .= "1. Main topic and theme\n";
        $prompt .= "2. Key concepts and subjects\n";
        $prompt .= "3. Emotional tone\n";
        $prompt .= "4. Target audience\n";
        $prompt .= "5. Suggested image types that would be most relevant\n";
        $prompt .= "6. SEO keywords for image alt text\n";
        $prompt .= "Format as JSON with keys: topic, concepts, tone, audience, image_types, seo_keywords";

        $response = wp_remote_post('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=' . $api_key, [
            'headers' => ['Content-Type' => 'application/json'],
            'body' => json_encode([
                'contents' => [
                    ['parts' => [['text' => $prompt]]]
                ]
            ]),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            return $this->basic_content_analysis($post);
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            $ai_response = $data['candidates'][0]['content']['parts'][0]['text'];

            // Extract JSON from AI response
            preg_match('/\{.*\}/s', $ai_response, $json_matches);

            if (!empty($json_matches[0])) {
                $analysis = json_decode($json_matches[0], true);

                if ($analysis) {
                    // Cache for 24 hours
                    set_transient($cache_key, $analysis, DAY_IN_SECONDS);
                    return $analysis;
                }
            }
        }

        return $this->basic_content_analysis($post);
    }

    private function basic_content_analysis($post) {
        $keywords = $this->extract_keywords($post->post_title . ' ' . $post->post_content);

        return [
            'topic' => $post->post_title,
            'concepts' => array_slice($keywords, 0, 5),
            'tone' => 'informative',
            'audience' => 'general',
            'image_types' => ['relevant', 'professional', 'high-quality'],
            'seo_keywords' => array_slice($keywords, 0, 10)
        ];
    }

    // ENHANCED IMAGE EXTRACTION WITH RELEVANCE SCORING

    private function extract_and_score_content_images($post, $content_analysis) {
        $image_ids = [];

        // Extract wp-image-* classes
        preg_match_all('/wp-image-(\d+)/', $post->post_content, $matches);

        if (!empty($matches[1])) {
            foreach ($matches[1] as $id) {
                if (wp_attachment_is_image($id)) {
                    $score = $this->calculate_image_relevance_score($id, $post, $content_analysis);
                    $image_ids[] = [
                        'id' => intval($id),
                        'score' => $score,
                        'source' => 'content'
                    ];
                }
            }
        }

        // Sort by relevance score (highest first)
        usort($image_ids, function($a, $b) {
            return $b['score'] <=> $a['score'];
        });

        return $image_ids;
    }
    
    // ADVANCED RELEVANCE MATCHING WITH AI

    private function find_highly_relevant_images($post, $content_analysis) {
        $scored_images = [];

        // Search by keywords from AI analysis
        $search_terms = array_merge(
            $content_analysis['concepts'] ?? [],
            $content_analysis['seo_keywords'] ?? []
        );

        if (empty($search_terms)) {
            return [];
        }

        // Search in multiple ways for better coverage
        $search_queries = [
            implode(' ', array_slice($search_terms, 0, 3)),
            implode(' ', array_slice($search_terms, 3, 3)),
            $content_analysis['topic'] ?? $post->post_title
        ];

        foreach ($search_queries as $query) {
            $args = [
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'post_status' => 'inherit',
                'posts_per_page' => 10,
                's' => $query
            ];

            $images = get_posts($args);

            foreach ($images as $image) {
                if (!isset($scored_images[$image->ID])) {
                    $score = $this->calculate_image_relevance_score($image->ID, $post, $content_analysis);
                    $scored_images[$image->ID] = [
                        'id' => $image->ID,
                        'score' => $score,
                        'source' => 'library_search'
                    ];
                }
            }
        }

        // Also search by image metadata and alt text
        $this->search_by_image_metadata($scored_images, $post, $content_analysis);

        // Sort by relevance score
        uasort($scored_images, function($a, $b) {
            return $b['score'] <=> $a['score'];
        });

        // Return top 5 matches
        return array_slice(array_values($scored_images), 0, 5);
    }

    private function search_by_image_metadata($scored_images, $post, $content_analysis) {
        global $wpdb;

        $keywords = array_merge(
            $content_analysis['concepts'] ?? [],
            $content_analysis['seo_keywords'] ?? []
        );

        if (empty($keywords)) {
            return;
        }

        // Search in image alt text and captions
        $keyword_pattern = implode('|', array_map('preg_quote', $keywords));

        $query = $wpdb->prepare(
            "SELECT p.ID, pm.meta_value as alt_text, p.post_excerpt as caption, p.post_title
             FROM {$wpdb->posts} p
             LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_wp_attachment_image_alt'
             WHERE p.post_type = 'attachment'
             AND p.post_mime_type LIKE 'image/%'
             AND p.post_status = 'inherit'
             AND (
                 pm.meta_value REGEXP %s
                 OR p.post_excerpt REGEXP %s
                 OR p.post_title REGEXP %s
             )",
            $keyword_pattern,
            $keyword_pattern,
            $keyword_pattern
        );

        $results = $wpdb->get_results($query);

        foreach ($results as $result) {
            if (!isset($scored_images[$result->ID])) {
                $score = $this->calculate_image_relevance_score($result->ID, $post, $content_analysis);
                $scored_images[$result->ID] = [
                    'id' => $result->ID,
                    'score' => $score,
                    'source' => 'metadata_search'
                ];
            }
        }
    }
    
    // ADVANCED IMAGE RELEVANCE SCORING ALGORITHM

    private function calculate_image_relevance_score($image_id, $post, $content_analysis) {
        $score = 0;
        $max_score = 100;

        $image = get_post($image_id);
        if (!$image) {
            return 0;
        }

        $alt_text = get_post_meta($image_id, '_wp_attachment_image_alt', true);
        $caption = $image->post_excerpt;
        $title = $image->post_title;
        $description = $image->post_content;

        // Combine all image text for analysis
        $image_text = strtolower($alt_text . ' ' . $caption . ' ' . $title . ' ' . $description);
        $post_text = strtolower($post->post_title . ' ' . wp_strip_all_tags($post->post_content));

        // 1. Keyword matching (40 points max)
        $keywords = array_merge(
            $content_analysis['concepts'] ?? [],
            $content_analysis['seo_keywords'] ?? []
        );

        $keyword_matches = 0;
        foreach ($keywords as $keyword) {
            if (strpos($image_text, strtolower($keyword)) !== false) {
                $keyword_matches++;
            }
        }

        $keyword_score = min(40, ($keyword_matches / max(1, count($keywords))) * 40);
        $score += $keyword_score;

        // 2. Title relevance (25 points max)
        $title_words = explode(' ', strtolower($post->post_title));
        $title_matches = 0;

        foreach ($title_words as $word) {
            if (strlen($word) > 3 && strpos($image_text, $word) !== false) {
                $title_matches++;
            }
        }

        $title_score = min(25, ($title_matches / max(1, count($title_words))) * 25);
        $score += $title_score;

        // 3. Image quality factors (20 points max)
        $metadata = wp_get_attachment_metadata($image_id);
        $quality_score = 0;

        if ($metadata) {
            // Resolution quality (10 points)
            $width = $metadata['width'] ?? 0;
            $height = $metadata['height'] ?? 0;

            if ($width >= 1200 && $height >= 630) {
                $quality_score += 10; // Perfect for social sharing
            } elseif ($width >= 800 && $height >= 400) {
                $quality_score += 7; // Good quality
            } elseif ($width >= 400 && $height >= 200) {
                $quality_score += 4; // Acceptable
            }

            // File size optimization (5 points)
            $file_size = $metadata['filesize'] ?? 0;
            if ($file_size > 0) {
                if ($file_size < 200000) { // Less than 200KB
                    $quality_score += 5;
                } elseif ($file_size < 500000) { // Less than 500KB
                    $quality_score += 3;
                } elseif ($file_size < 1000000) { // Less than 1MB
                    $quality_score += 1;
                }
            }

            // Alt text presence (5 points)
            if (!empty($alt_text)) {
                $quality_score += 5;
            }
        }

        $score += $quality_score;

        // 4. Semantic similarity (15 points max)
        $semantic_score = $this->calculate_semantic_similarity($image_text, $post_text, $content_analysis);
        $score += $semantic_score;

        return min($max_score, $score);
    }

    private function calculate_semantic_similarity($image_text, $post_text, $content_analysis) {
        $score = 0;

        // Check for topic alignment
        $topic = strtolower($content_analysis['topic'] ?? '');
        if (!empty($topic) && strpos($image_text, $topic) !== false) {
            $score += 8;
        }

        // Check for tone alignment
        $tone = strtolower($content_analysis['tone'] ?? '');
        $tone_keywords = [
            'professional' => ['business', 'corporate', 'office', 'formal'],
            'casual' => ['lifestyle', 'everyday', 'natural', 'relaxed'],
            'technical' => ['diagram', 'chart', 'technical', 'illustration'],
            'creative' => ['artistic', 'creative', 'design', 'colorful']
        ];

        if (isset($tone_keywords[$tone])) {
            foreach ($tone_keywords[$tone] as $tone_word) {
                if (strpos($image_text, $tone_word) !== false) {
                    $score += 2;
                    break;
                }
            }
        }

        // Check for audience alignment
        $audience = strtolower($content_analysis['audience'] ?? '');
        if ($audience === 'professional' && strpos($image_text, 'business') !== false) {
            $score += 3;
        } elseif ($audience === 'general' && strpos($image_text, 'people') !== false) {
            $score += 2;
        }

        return min(15, $score);
    }

    // SEO OPTIMIZATION FOR SELECTED IMAGES

    private function optimize_image_for_seo($image_id, $post, $content_analysis) {
        // Generate optimized alt text if missing or poor quality
        $current_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);

        if (empty($current_alt) || strlen($current_alt) < 10) {
            $optimized_alt = $this->generate_seo_optimized_alt_text($image_id, $post, $content_analysis);
            if ($optimized_alt) {
                update_post_meta($image_id, '_wp_attachment_image_alt', $optimized_alt);
            }
        }

        // Optimize image title if needed
        $image = get_post($image_id);
        if ($image && (empty($image->post_title) || strpos($image->post_title, 'IMG_') !== false)) {
            $optimized_title = $this->generate_seo_optimized_title($image_id, $post, $content_analysis);
            if ($optimized_title) {
                wp_update_post([
                    'ID' => $image_id,
                    'post_title' => $optimized_title
                ]);
            }
        }

        // Add structured data
        $this->add_image_structured_data($image_id, $post, $content_analysis);

        // Log optimization
        $this->log_seo_optimization($image_id, $post->ID);
    }

    private function generate_seo_optimized_alt_text($image_id, $post, $content_analysis) {
        $keywords = array_slice($content_analysis['seo_keywords'] ?? [], 0, 3);
        $topic = $content_analysis['topic'] ?? $post->post_title;

        if (empty($keywords)) {
            return null;
        }

        // Create natural, SEO-friendly alt text
        $alt_templates = [
            "{keyword1} related to {topic}",
            "{topic} showing {keyword1} and {keyword2}",
            "Professional {keyword1} for {topic}",
            "{keyword1} illustration for {topic} content",
            "High-quality {keyword1} image about {topic}"
        ];

        $template = $alt_templates[array_rand($alt_templates)];

        $replacements = [
            '{topic}' => $topic,
            '{keyword1}' => $keywords[0] ?? '',
            '{keyword2}' => $keywords[1] ?? '',
            '{keyword3}' => $keywords[2] ?? ''
        ];

        $alt_text = str_replace(array_keys($replacements), array_values($replacements), $template);

        // Clean up and ensure proper length
        $alt_text = trim(preg_replace('/\s+/', ' ', $alt_text));
        $alt_text = ucfirst($alt_text);

        // Ensure it's not too long (125 characters is optimal)
        if (strlen($alt_text) > 125) {
            $alt_text = substr($alt_text, 0, 122) . '...';
        }

        return $alt_text;
    }

    private function generate_seo_optimized_title($image_id, $post, $content_analysis) {
        $keywords = array_slice($content_analysis['seo_keywords'] ?? [], 0, 2);
        $topic = $content_analysis['topic'] ?? $post->post_title;

        if (empty($keywords)) {
            return null;
        }

        $title_templates = [
            "{keyword1} - {topic}",
            "{topic} {keyword1}",
            "Professional {keyword1} for {topic}",
            "{keyword1} and {keyword2} - {topic}"
        ];

        $template = $title_templates[array_rand($title_templates)];

        $replacements = [
            '{topic}' => $topic,
            '{keyword1}' => $keywords[0] ?? '',
            '{keyword2}' => $keywords[1] ?? ''
        ];

        $title = str_replace(array_keys($replacements), array_values($replacements), $template);
        $title = trim(preg_replace('/\s+/', ' ', $title));

        return ucwords($title);
    }

    private function add_image_structured_data($image_id, $post, $content_analysis) {
        // Store structured data as post meta for later use
        $structured_data = [
            'context' => 'https://schema.org',
            'type' => 'ImageObject',
            'contentUrl' => wp_get_attachment_url($image_id),
            'description' => get_post_meta($image_id, '_wp_attachment_image_alt', true),
            'keywords' => implode(', ', $content_analysis['seo_keywords'] ?? []),
            'about' => $content_analysis['topic'] ?? $post->post_title,
            'associatedArticle' => get_permalink($post->ID)
        ];

        update_post_meta($image_id, '_ufio_structured_data', $structured_data);
    }

    private function log_seo_optimization($image_id, $post_id) {
        $log_entry = [
            'timestamp' => current_time('mysql'),
            'image_id' => $image_id,
            'post_id' => $post_id,
            'action' => 'seo_optimization',
            'status' => 'completed'
        ];

        $existing_log = get_option('ufio_seo_optimization_log', []);
        $existing_log[] = $log_entry;

        // Keep only last 100 entries
        if (count($existing_log) > 100) {
            $existing_log = array_slice($existing_log, -100);
        }

        update_option('ufio_seo_optimization_log', $existing_log);
    }

    private function log_missing_image_suggestion($post, $content_analysis) {
        $suggestion = [
            'post_id' => $post->ID,
            'post_title' => $post->post_title,
            'suggested_keywords' => $content_analysis['seo_keywords'] ?? [],
            'suggested_image_types' => $content_analysis['image_types'] ?? [],
            'timestamp' => current_time('mysql')
        ];

        $suggestions = get_option('ufio_missing_image_suggestions', []);
        $suggestions[] = $suggestion;

        // Keep only last 50 suggestions
        if (count($suggestions) > 50) {
            $suggestions = array_slice($suggestions, -50);
        }

        update_option('ufio_missing_image_suggestions', $suggestions);
    }

    private function extract_keywords($text) {
        $text = wp_strip_all_tags($text);
        $text = preg_replace('/[^\w\s]/', ' ', $text);
        $words = str_word_count(strtolower($text), 1);

        // Filter out common words
        $stop_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];

        $words = array_filter($words, function($word) use ($stop_words) {
            return strlen($word) > 3 && !in_array($word, $stop_words);
        });

        $word_counts = array_count_values($words);
        arsort($word_counts);

        return array_keys(array_slice($word_counts, 0, 10));
    }

    // AUTO-PROCESSING HOOKS FOR NEW CONTENT

    public function auto_process_post($post_id, $post) {
        // Only process if auto-assign is enabled
        if (!$this->options['auto_assign_featured']) {
            return;
        }

        // Skip if not a supported post type
        if (!in_array($post->post_type, ['post', 'page'])) {
            return;
        }

        // Skip if post is not published
        if ($post->post_status !== 'publish') {
            return;
        }

        // Skip if already has featured image
        if (get_post_thumbnail_id($post_id)) {
            return;
        }

        // Process in background to avoid slowing down the save
        wp_schedule_single_event(time() + 10, 'ufio_process_post_background', [$post_id]);
    }

    public function auto_process_new_post($post_id, $post) {
        // Only for newly published posts
        if ($post->post_status === 'publish' && !get_post_thumbnail_id($post_id)) {
            $this->auto_process_post($post_id, $post);
        }
    }

    public function auto_optimize_new_image($attachment_id) {
        // Only process images
        if (!wp_attachment_is_image($attachment_id)) {
            return;
        }

        // Schedule SEO optimization
        wp_schedule_single_event(time() + 5, 'ufio_optimize_image_background', [$attachment_id]);
    }

    // SEO ENHANCEMENT HOOKS

    public function enhance_image_attributes($attr, $attachment, $size) {
        // Add structured data attributes
        $attr['itemscope'] = '';
        $attr['itemtype'] = 'https://schema.org/ImageObject';

        // Enhance alt text if empty or poor quality
        if (empty($attr['alt']) || strlen($attr['alt']) < 10) {
            $enhanced_alt = get_post_meta($attachment->ID, '_ufio_enhanced_alt', true);
            if ($enhanced_alt) {
                $attr['alt'] = $enhanced_alt;
            }
        }

        // Add loading optimization
        if (!isset($attr['loading'])) {
            $attr['loading'] = 'lazy';
        }

        // Add decoding optimization
        if (!isset($attr['decoding'])) {
            $attr['decoding'] = 'async';
        }

        return $attr;
    }

    public function output_image_structured_data() {
        if (!is_singular()) {
            return;
        }

        global $post;
        $featured_image_id = get_post_thumbnail_id($post->ID);

        if (!$featured_image_id) {
            return;
        }

        $structured_data = get_post_meta($featured_image_id, '_ufio_structured_data', true);

        if ($structured_data) {
            echo '<script type="application/ld+json">' . json_encode($structured_data, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
        }
    }

    // BACKGROUND PROCESSING METHODS

    public function optimize_single_image($attachment_id) {
        if (!wp_attachment_is_image($attachment_id)) {
            return;
        }

        // Get the post this image might be associated with
        $posts = get_posts([
            'post_type' => ['post', 'page'],
            'post_status' => 'publish',
            'meta_query' => [
                [
                    'key' => '_thumbnail_id',
                    'value' => $attachment_id,
                    'compare' => '='
                ]
            ],
            'posts_per_page' => 1
        ]);

        if (!empty($posts)) {
            $post = $posts[0];
            $content_analysis = $this->analyze_post_content_with_ai($post);
            $this->optimize_image_for_seo($attachment_id, $post, $content_analysis);
        } else {
            // Optimize based on image metadata only
            $this->optimize_orphaned_image($attachment_id);
        }
    }

    private function optimize_orphaned_image($attachment_id) {
        $image = get_post($attachment_id);
        if (!$image) {
            return;
        }

        // Basic optimization for images not associated with posts
        $alt_text = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);

        if (empty($alt_text)) {
            // Generate basic alt text from filename and title
            $filename = basename(get_attached_file($attachment_id));
            $title = $image->post_title;

            if (!empty($title) && $title !== $filename) {
                $basic_alt = ucfirst(str_replace(['-', '_'], ' ', $title));
                update_post_meta($attachment_id, '_wp_attachment_image_alt', $basic_alt);
            }
        }
    }
    
    private function get_dashboard_stats() {
        global $wpdb;
        
        $total_posts = wp_count_posts()->publish;
        $total_images = wp_count_attachments('image')['image'] ?? 0;
        
        $posts_with_featured = $wpdb->get_var(
            "SELECT COUNT(DISTINCT p.ID) 
             FROM {$wpdb->posts} p 
             INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
             WHERE p.post_status = 'publish' 
             AND p.post_type = 'post' 
             AND pm.meta_key = '_thumbnail_id' 
             AND pm.meta_value != ''"
        );
        
        $posts_without_featured = $total_posts - $posts_with_featured;
        $coverage_percentage = $total_posts > 0 ? round(($posts_with_featured / $total_posts) * 100, 1) : 0;
        
        return [
            'total_posts' => $total_posts,
            'total_images' => $total_images,
            'posts_with_featured' => $posts_with_featured,
            'posts_without_featured' => $posts_without_featured,
            'coverage_percentage' => $coverage_percentage,
            'api_configured' => !empty($this->options['gemini_api_key']),
            'processing_stats' => $this->get_processing_stats()
        ];
    }
    
    private function get_processing_stats() {
        return get_option('ufio_processing_stats', [
            'total_processed' => 0,
            'total_errors' => 0,
            'last_run' => null,
            'success_rate' => 0
        ]);
    }
    
    private function update_processing_stats($processed, $errors) {
        $stats = $this->get_processing_stats();
        $stats['total_processed'] += $processed;
        $stats['total_errors'] += $errors;
        $stats['last_run'] = current_time('mysql');
        
        $total_attempts = $stats['total_processed'] + $stats['total_errors'];
        $stats['success_rate'] = $total_attempts > 0 ? round(($stats['total_processed'] / $total_attempts) * 100, 1) : 0;
        
        update_option('ufio_processing_stats', $stats);
    }
    
    public function activate() {
        add_option('ufio_options', $this->options);
        
        // Create upload directories
        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/ufio-cache/';
        
        if (!file_exists($cache_dir)) {
            wp_mkdir_p($cache_dir);
            file_put_contents($cache_dir . 'index.php', '<?php // Silence is golden');
        }
        
        flush_rewrite_rules();
    }

    // PROFESSIONAL PAGE RENDERING METHODS

    public function render_dashboard() {
        $stats = $this->get_dashboard_stats();
        ?>
        <div class="wrap ufio-dashboard">
            <h1><?php _e('Ultra Featured Image Optimizer Pro', 'ultra-featured-image-optimizer'); ?></h1>

            <div class="ufio-stats-grid">
                <div class="ufio-stat-card primary">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['total_posts']); ?></div>
                    <div class="ufio-stat-label"><?php _e('Total Posts', 'ultra-featured-image-optimizer'); ?></div>
                </div>

                <div class="ufio-stat-card success">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['posts_with_featured']); ?></div>
                    <div class="ufio-stat-label"><?php _e('With Featured Images', 'ultra-featured-image-optimizer'); ?></div>
                </div>

                <div class="ufio-stat-card warning">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['posts_without_featured']); ?></div>
                    <div class="ufio-stat-label"><?php _e('Need Processing', 'ultra-featured-image-optimizer'); ?></div>
                </div>

                <div class="ufio-stat-card info">
                    <div class="ufio-stat-number"><?php echo esc_html($stats['coverage_percentage']); ?>%</div>
                    <div class="ufio-stat-label"><?php _e('Coverage Rate', 'ultra-featured-image-optimizer'); ?></div>
                </div>
            </div>

            <div class="ufio-grid">
                <div class="ufio-card">
                    <h2><?php _e('Quick Actions', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-action-buttons">
                        <a href="<?php echo admin_url('admin.php?page=ufio-bulk'); ?>" class="ufio-btn primary">
                            <span class="dashicons dashicons-images-alt2"></span>
                            <?php _e('Bulk Processing', 'ultra-featured-image-optimizer'); ?>
                        </a>

                        <a href="<?php echo admin_url('admin.php?page=ufio-settings'); ?>" class="ufio-btn secondary">
                            <span class="dashicons dashicons-admin-settings"></span>
                            <?php _e('Settings', 'ultra-featured-image-optimizer'); ?>
                        </a>

                        <button type="button" class="ufio-btn info" id="ufio-refresh-stats">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Refresh Stats', 'ultra-featured-image-optimizer'); ?>
                        </button>
                    </div>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('System Status', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-status-list">
                        <div class="ufio-status-item">
                            <span class="ufio-status-indicator success"></span>
                            <span class="ufio-status-label"><?php _e('Plugin Active', 'ultra-featured-image-optimizer'); ?></span>
                        </div>

                        <div class="ufio-status-item">
                            <span class="ufio-status-indicator <?php echo $stats['api_configured'] ? 'success' : 'warning'; ?>"></span>
                            <span class="ufio-status-label">
                                <?php echo $stats['api_configured'] ? __('API Configured', 'ultra-featured-image-optimizer') : __('API Key Needed', 'ultra-featured-image-optimizer'); ?>
                            </span>
                        </div>

                        <div class="ufio-status-item">
                            <span class="ufio-status-indicator success"></span>
                            <span class="ufio-status-label"><?php _e('Background Processing Ready', 'ultra-featured-image-optimizer'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ufio-card">
                <h2><?php _e('Processing Statistics', 'ultra-featured-image-optimizer'); ?></h2>

                <div class="ufio-progress-section">
                    <div class="ufio-progress-item">
                        <label><?php _e('Featured Image Coverage', 'ultra-featured-image-optimizer'); ?></label>
                        <div class="ufio-progress-bar">
                            <div class="ufio-progress-fill" style="width: <?php echo esc_attr($stats['coverage_percentage']); ?>%"></div>
                            <span class="ufio-progress-text"><?php echo esc_html($stats['coverage_percentage']); ?>%</span>
                        </div>
                    </div>
                </div>

                <?php if ($stats['processing_stats']['total_processed'] > 0): ?>
                <div class="ufio-processing-history">
                    <h3><?php _e('Recent Processing', 'ultra-featured-image-optimizer'); ?></h3>
                    <p><?php printf(__('Processed %d posts with %d%% success rate', 'ultra-featured-image-optimizer'),
                        $stats['processing_stats']['total_processed'],
                        $stats['processing_stats']['success_rate']); ?></p>

                    <?php if ($stats['processing_stats']['last_run']): ?>
                    <p class="ufio-last-run">
                        <?php printf(__('Last run: %s', 'ultra-featured-image-optimizer'),
                            human_time_diff(strtotime($stats['processing_stats']['last_run']), current_time('timestamp')) . ' ago'); ?>
                    </p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    public function render_bulk_page() {
        ?>
        <div class="wrap ufio-bulk-page">
            <h1><?php _e('Bulk Processing', 'ultra-featured-image-optimizer'); ?></h1>

            <div class="ufio-card">
                <h2><?php _e('Process Posts Without Featured Images', 'ultra-featured-image-optimizer'); ?></h2>
                <p><?php _e('Automatically find and assign featured images to posts that don\'t have them.', 'ultra-featured-image-optimizer'); ?></p>

                <div class="ufio-bulk-controls">
                    <div class="ufio-form-group">
                        <label for="ufio-post-type"><?php _e('Post Type:', 'ultra-featured-image-optimizer'); ?></label>
                        <select id="ufio-post-type">
                            <option value="post"><?php _e('Posts', 'ultra-featured-image-optimizer'); ?></option>
                            <option value="page"><?php _e('Pages', 'ultra-featured-image-optimizer'); ?></option>
                        </select>
                    </div>

                    <div class="ufio-form-group">
                        <label for="ufio-batch-size"><?php _e('Batch Size:', 'ultra-featured-image-optimizer'); ?></label>
                        <select id="ufio-batch-size">
                            <option value="5">5 posts</option>
                            <option value="10" selected>10 posts</option>
                            <option value="20">20 posts</option>
                            <option value="50">50 posts</option>
                        </select>
                    </div>

                    <button type="button" class="ufio-btn primary large" id="ufio-start-bulk">
                        <span class="dashicons dashicons-images-alt2"></span>
                        <?php _e('Start Processing', 'ultra-featured-image-optimizer'); ?>
                    </button>
                </div>

                <div id="ufio-bulk-progress" class="ufio-progress-container" style="display: none;">
                    <div class="ufio-progress-bar">
                        <div class="ufio-progress-fill" style="width: 0%"></div>
                        <span class="ufio-progress-text">0%</span>
                    </div>
                    <div class="ufio-progress-details">
                        <span id="ufio-progress-status"><?php _e('Initializing...', 'ultra-featured-image-optimizer'); ?></span>
                    </div>
                </div>

                <div id="ufio-bulk-results" class="ufio-results-container" style="display: none;">
                    <h3><?php _e('Processing Results', 'ultra-featured-image-optimizer'); ?></h3>
                    <div id="ufio-results-content"></div>
                </div>
            </div>

            <div class="ufio-card">
                <h2><?php _e('Individual Post Processing', 'ultra-featured-image-optimizer'); ?></h2>
                <p><?php _e('Process a specific post by entering its ID.', 'ultra-featured-image-optimizer'); ?></p>

                <div class="ufio-single-controls">
                    <div class="ufio-form-group">
                        <label for="ufio-single-post-id"><?php _e('Post ID:', 'ultra-featured-image-optimizer'); ?></label>
                        <input type="number" id="ufio-single-post-id" placeholder="<?php _e('Enter post ID', 'ultra-featured-image-optimizer'); ?>" min="1">
                    </div>

                    <button type="button" class="ufio-btn secondary" id="ufio-process-single">
                        <span class="dashicons dashicons-admin-media"></span>
                        <?php _e('Process Single Post', 'ultra-featured-image-optimizer'); ?>
                    </button>
                </div>

                <div id="ufio-single-result" class="ufio-single-result" style="display: none;"></div>
            </div>
        </div>
        <?php
    }

    public function render_settings() {
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['ufio_nonce'] ?? '', 'ufio_settings')) {
            $this->options['gemini_api_key'] = sanitize_text_field($_POST['gemini_api_key'] ?? '');
            $this->options['auto_assign_featured'] = isset($_POST['auto_assign_featured']);
            $this->options['enable_seo_optimization'] = isset($_POST['enable_seo_optimization']);
            $this->options['background_processing'] = isset($_POST['background_processing']);
            $this->options['image_quality'] = intval($_POST['image_quality'] ?? 85);
            $this->options['max_images_per_batch'] = intval($_POST['max_images_per_batch'] ?? 10);

            update_option('ufio_options', $this->options);

            echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'ultra-featured-image-optimizer') . '</p></div>';
        }
        ?>
        <div class="wrap ufio-settings-page">
            <h1><?php _e('Settings', 'ultra-featured-image-optimizer'); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('ufio_settings', 'ufio_nonce'); ?>

                <div class="ufio-card">
                    <h2><?php _e('AI Configuration', 'ultra-featured-image-optimizer'); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Gemini API Key', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <input type="password" name="gemini_api_key" value="<?php echo esc_attr($this->options['gemini_api_key']); ?>" class="regular-text" />
                                <p class="description">
                                    <?php _e('Get your API key from', 'ultra-featured-image-optimizer'); ?>
                                    <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                                </p>
                                <button type="button" class="button" id="ufio-test-api-btn">
                                    <?php _e('Test API Connection', 'ultra-featured-image-optimizer'); ?>
                                </button>
                                <div id="ufio-api-test-result"></div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('Processing Options', 'ultra-featured-image-optimizer'); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Auto-assign Featured Images', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_assign_featured" value="1" <?php checked($this->options['auto_assign_featured']); ?> />
                                    <?php _e('Automatically assign featured images to new posts', 'ultra-featured-image-optimizer'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Background Processing', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="background_processing" value="1" <?php checked($this->options['background_processing']); ?> />
                                    <?php _e('Enable background processing for better performance', 'ultra-featured-image-optimizer'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Max Images per Batch', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <select name="max_images_per_batch">
                                    <option value="5" <?php selected($this->options['max_images_per_batch'], 5); ?>>5</option>
                                    <option value="10" <?php selected($this->options['max_images_per_batch'], 10); ?>>10</option>
                                    <option value="20" <?php selected($this->options['max_images_per_batch'], 20); ?>>20</option>
                                    <option value="50" <?php selected($this->options['max_images_per_batch'], 50); ?>>50</option>
                                </select>
                                <p class="description"><?php _e('Number of images to process in each batch', 'ultra-featured-image-optimizer'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('SEO & Optimization', 'ultra-featured-image-optimizer'); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('SEO Optimization', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="enable_seo_optimization" value="1" <?php checked($this->options['enable_seo_optimization']); ?> />
                                    <?php _e('Enable SEO optimization features', 'ultra-featured-image-optimizer'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Image Quality', 'ultra-featured-image-optimizer'); ?></th>
                            <td>
                                <input type="range" name="image_quality" min="60" max="100" value="<?php echo esc_attr($this->options['image_quality']); ?>" class="ufio-range" />
                                <span class="ufio-range-value"><?php echo esc_html($this->options['image_quality']); ?>%</span>
                                <p class="description"><?php _e('Image compression quality (higher = better quality, larger file size)', 'ultra-featured-image-optimizer'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <?php submit_button(__('Save Settings', 'ultra-featured-image-optimizer'), 'primary', 'submit', true, ['class' => 'ufio-btn primary large']); ?>
            </form>
        </div>
        <?php
    }

    public function render_analytics() {
        $stats = $this->get_dashboard_stats();
        ?>
        <div class="wrap ufio-analytics-page">
            <h1><?php _e('Analytics & Performance', 'ultra-featured-image-optimizer'); ?></h1>

            <div class="ufio-analytics-grid">
                <div class="ufio-card">
                    <h2><?php _e('Processing Overview', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-analytics-stats">
                        <div class="ufio-analytics-item">
                            <span class="ufio-analytics-number"><?php echo esc_html($stats['processing_stats']['total_processed']); ?></span>
                            <span class="ufio-analytics-label"><?php _e('Total Processed', 'ultra-featured-image-optimizer'); ?></span>
                        </div>

                        <div class="ufio-analytics-item">
                            <span class="ufio-analytics-number"><?php echo esc_html($stats['processing_stats']['success_rate']); ?>%</span>
                            <span class="ufio-analytics-label"><?php _e('Success Rate', 'ultra-featured-image-optimizer'); ?></span>
                        </div>

                        <div class="ufio-analytics-item">
                            <span class="ufio-analytics-number"><?php echo esc_html($stats['processing_stats']['total_errors']); ?></span>
                            <span class="ufio-analytics-label"><?php _e('Total Errors', 'ultra-featured-image-optimizer'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="ufio-card">
                    <h2><?php _e('System Performance', 'ultra-featured-image-optimizer'); ?></h2>

                    <div class="ufio-performance-metrics">
                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('PHP Version:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(PHP_VERSION); ?></span>
                        </div>

                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('Memory Limit:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(ini_get('memory_limit')); ?></span>
                        </div>

                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('Max Execution Time:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(ini_get('max_execution_time')); ?>s</span>
                        </div>

                        <div class="ufio-metric">
                            <span class="ufio-metric-label"><?php _e('WordPress Version:', 'ultra-featured-image-optimizer'); ?></span>
                            <span class="ufio-metric-value"><?php echo esc_html(get_bloginfo('version')); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ufio-card">
                <h2><?php _e('Cache Management', 'ultra-featured-image-optimizer'); ?></h2>
                <p><?php _e('Manage plugin cache and temporary data.', 'ultra-featured-image-optimizer'); ?></p>

                <div class="ufio-cache-controls">
                    <button type="button" class="ufio-btn warning" id="ufio-clear-cache-btn">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('Clear All Cache', 'ultra-featured-image-optimizer'); ?>
                    </button>

                    <div id="ufio-cache-result" class="ufio-cache-result"></div>
                </div>
            </div>
        </div>
        <?php
    }
}

// Initialize the plugin
add_action('plugins_loaded', function() {
    UltraFeaturedImageOptimizerPro::get_instance();
});

// Helper function
function ufio_pro() {
    return UltraFeaturedImageOptimizerPro::get_instance();
}
